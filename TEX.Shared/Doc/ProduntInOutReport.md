## **TEX产品出入库和库存查询统计方案规划**

### **一、现状分析**

**现有功能：**

- ✅ 基础列表查询：ProductInboundBillListVM、ProductOutboundBillListVM、ProductStockListVM
- ✅ 基础搜索功能：按日期、单号、客户、仓库等条件筛选
- ✅ 订单仪表板：OrderDashboard（月度下单统计、图表展示）
- ✅ 三级联动CRUD：完整的Bill-Lot-Roll增删改查功能

**缺失功能：**

- ❌ 出入库统计分析页面
- ❌ 多维度数据透视分析
- ❌ 业务趋势分析报表
- ❌ 统计数据钻取查询功能
- ❌ 按订单维度的库存状态监控

### **二、查询统计方案设计**

#### **方案A：统计优先查询方案**

**适用场景：** 快速实现，满足日常统计查询需求 **实现复杂度：** ⭐⭐ **开发周期：** 1-2周

**核心功能：**

1. **统计数据优先展示**
    - 基于现有搜索条件直接显示汇总统计（总件数、总重量、总米数、总码数）
    - 按时间段、客户、产品、订单等维度分组统计
    - 统计结果支持图表可视化（柱状图、饼图）
2. **钻取查询功能**
    - 点击统计数据可钻取到对应的明细列表
    - 支持多级钻取（汇总→分组→明细）
    - 钻取时保持原有查询条件
3. **按订单维度库存查询**
    - 按订单明细查看库存分布
    - 订单完成度统计
    - 订单库存状态跟踪

#### **方案B：多维度统计分析方案**

**适用场景：** 专业数据分析，支持决策 **实现复杂度：** ⭐⭐⭐⭐ **开发周期：** 3-4周

**核心功能：**

1. **多维度数据透视**
    - 按客户、产品、时间、仓库、订单等维度交叉分析
    - 支持多级钻取查询（汇总→分组→明细→单据）
    - 动态图表展示（柱状图、饼图、趋势图、热力图）
2. **订单导向的业务分析**
    - 订单执行进度分析
    - 客户订单完成情况统计
    - 产品交付周期分析
    - 订单库存匹配度分析
3. **高级统计查询功能**
    - 自定义统计维度组合
    - 保存常用统计模板
    - 统计结果导出和分享
    - 定时统计报表生成

#### **方案C：订单驱动的智能统计平台**

**适用场景：** 企业级数据平台，长期规划 **实现复杂度：** ⭐⭐⭐⭐⭐ **开发周期：** 6-8周

**核心功能：**

1. **订单执行监控大屏**
    - 实时订单执行状态
    - 出入库实时流水
    - 订单交付进度监控
    - 关键业务KPI指标
2. **订单导向的预测分析**
    - 订单交付时间预测
    - 客户需求趋势分析
    - 生产计划优化建议
    - 季节性订单模式分析
3. **移动端统计查询**
    - 移动端统计查询应用
    - 订单状态推送通知
    - 离线统计数据同步
    - 现场数据采集支持

### **三、推荐实施方案**

考虑到TEX项目的实际情况和现有技术架构，我推荐采用**渐进式实施策略**：

**第一阶段：统计优先查询实现（优先级：高）**

- 新建展示统计数据页面
- 实现统计数据的钻取查询功能
- 添加按订单维度的库存查询
- 实现基础统计数据导出功能

**第二阶段：多维度统计分析（优先级：中）**

- 开发专门的多维度统计分析页面
- 实现订单导向的数据透视功能
- 添加动态图表可视化
- 构建统计模板保存和复用机制

**第三阶段：订单驱动智能平台（优先级：低）**

- 构建订单执行监控系统
- 集成订单导向的预测分析功能
- 开发移动端统计查询应用

### **四、具体页面设计建议**

#### **4.1 出入库统计查询页面**

**页面路径：** `/Finished/InOutboundStatistics`

**核心设计理念：** 统计数据优先，支持钻取查询

**功能模块：**
- **统计数据展示区**：基于搜索条件显示汇总统计（件数、重量、米数、码数）
- **多维度分组统计**：按时间、客户、产品、订单等维度分组展示
- **图表可视化**：柱状图、饼图展示统计结果
- **钻取查询功能**：点击统计数据钻取到明细列表
- **搜索条件保持**：钻取时保持原有查询条件

#### **4.2 订单库存统计页面**

**页面路径：** `/Finished/OrderStockStatistics`

**核心设计理念：** 按订单维度统计库存，支持订单执行跟踪

**功能模块：**
- **订单库存总览**：按订单明细统计当前库存
- **订单完成度统计**：订单执行进度可视化
- **库存分布分析**：按仓库、产品维度分析库存分布
- **订单状态跟踪**：订单从下单到交付的全流程跟踪

#### **4.3 多维度统计分析页面**

**页面路径：** `/Finished/MultiDimensionAnalysis`

**核心设计理念：** 支持自定义维度组合的高级统计分析

**功能模块：**
- **维度选择器**：支持客户、产品、时间、仓库、订单等维度组合
- **交叉分析表格**：多维度数据透视表
- **动态图表生成**：根据选择维度自动生成对应图表
- **统计模板管理**：保存和复用常用统计配置
- **高级钻取功能**：支持多级钻取（汇总→分组→明细→单据）

### **五、技术实现要点**

#### **5.1 统计数据优先的查询架构**
```csharp
// 统计查询优先，明细查询按需
public class StatisticsFirstQueryVM : BasePagedListVM<T, TSearcher>
{
    // 优先返回统计数据
    public StatisticsResult GetStatistics();

    // 支持钻取的明细查询
    public IEnumerable<T> GetDetailsByDrillDown(DrillDownParams params);
}
```

#### **5.2 钻取查询的实现模式**
- **保持查询上下文**：钻取时保留原始搜索条件
- **分层数据结构**：汇总→分组→明细→单据的层级关系
- **动态查询构建**：根据钻取层级动态构建查询条件

#### **5.3 订单导向的数据模型**
- **以OrderDetail为核心**：所有统计都基于订单明细维度
- **库存与订单关联**：ProductStock通过OrderDetailId关联订单
- **订单执行状态跟踪**：从下单→入库→库存→出库的全流程

**改进后的方案更加符合纺织外贸行业的业务特点：**
1. ✅ **统计数据优先**：用户首先看到汇总统计，而不是冗长的列表
2. ✅ **钻取查询支持**：从统计数据可以深入到具体明细
3. ✅ **订单导向设计**：完全按照订单生产的业务模式设计
4. ✅ **移除库存预警**：不需要传统制造业的库存预警功能

这个调整后的方案更加贴合您的实际业务需求和用户使用习惯。